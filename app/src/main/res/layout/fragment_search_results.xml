<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.search.SearchResultsFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Search Query -->
            <TextView
                android:id="@+id/tv_search_query"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:textAppearance="?attr/textAppearanceBody1"
                tools:text="Search query: hello" />

            <!-- Tickets Section -->
            <TextView
                android:id="@+id/tv_tickets_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:text="@string/search_tickets"
                android:textAppearance="?attr/textAppearanceHeadline6"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_tickets"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:padding="4dp"
                tools:itemCount="2"
                tools:listitem="@layout/item_ticket" />

            <TextView
                android:id="@+id/tv_no_tickets"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:text="@string/no_search_results"
                android:textAlignment="center"
                android:visibility="gone" />



            <!-- Knowledges Section -->
            <TextView
                android:id="@+id/tv_knowledges_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:text="@string/search_knowledges"
                android:textAppearance="?attr/textAppearanceHeadline6"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_knowledges"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:padding="4dp"
                tools:itemCount="2"
                tools:listitem="@layout/item_knowledge" />

            <TextView
                android:id="@+id/tv_no_knowledges"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:text="@string/no_search_results"
                android:textAlignment="center"
                android:visibility="gone" />

            <!-- FAQs Section -->
            <TextView
                android:id="@+id/tv_faqs_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:text="@string/search_faqs"
                android:textAppearance="?attr/textAppearanceHeadline6"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_faqs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:padding="4dp"
                tools:itemCount="2"
                tools:listitem="@layout/item_faq" />

            <TextView
                android:id="@+id/tv_no_faqs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:text="@string/no_search_results"
                android:textAlignment="center"
                android:visibility="gone" />

            <!-- Error View -->
            <TextView
                android:id="@+id/tv_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                android:textAlignment="center"
                android:textColor="@android:color/holo_red_dark"
                android:visibility="gone"
                tools:text="Error message"
                tools:visibility="visible" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
