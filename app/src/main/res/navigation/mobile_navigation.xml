<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="ir.rahavardit.ariel.ui.home.HomeFragment"
        android:label="@string/menu_home"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_nav_home_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_nav_home_to_editTransactionFragment"
            app:destination="@id/editTransactionFragment" />
    </fragment>

    <fragment
        android:id="@+id/nav_tickets"
        android:name="ir.rahavardit.ariel.ui.tickets.TicketsFragment"
        android:label="@string/menu_tickets"
        tools:layout="@layout/fragment_tickets">
        <argument
            android:name="status"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null" />
        <argument
            android:name="groupId"
            app:argType="integer"
            android:defaultValue="-1" />
        <action
            android:id="@+id/action_nav_tickets_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_nav_tickets_to_nav_new_ticket"
            app:destination="@id/nav_new_ticket" />
    </fragment>

    <fragment
        android:id="@+id/ticketDetailsFragment"
        android:name="ir.rahavardit.ariel.ui.ticketdetails.TicketDetailsFragment"
        android:label="@string/ticket_details"
        tools:layout="@layout/fragment_ticket_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_ticketDetailsFragment_to_nav_tickets"
            app:destination="@id/nav_tickets"
            app:popUpTo="@id/nav_tickets"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_ticket"
        android:name="ir.rahavardit.ariel.ui.newticket.NewTicketFragment"
        android:label="@string/new_ticket"
        tools:layout="@layout/fragment_new_ticket">
        <action
            android:id="@+id/action_nav_new_ticket_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_profile"
        android:name="ir.rahavardit.ariel.ui.profile.ProfileFragment"
        android:label="@string/menu_profile"
        tools:layout="@layout/fragment_profile" />

    <fragment
        android:id="@+id/nav_knowledges"
        android:name="ir.rahavardit.ariel.ui.knowledges.KnowledgesFragment"
        android:label="@string/menu_knowledges"
        tools:layout="@layout/fragment_knowledges">
        <action
            android:id="@+id/action_nav_knowledges_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment" />
        <action
            android:id="@+id/action_nav_knowledges_to_nav_new_knowledge"
            app:destination="@id/nav_new_knowledge" />
    </fragment>

    <fragment
        android:id="@+id/knowledgeDetailsFragment"
        android:name="ir.rahavardit.ariel.ui.knowledgedetails.KnowledgeDetailsFragment"
        android:label="@string/knowledge_details"
        tools:layout="@layout/fragment_knowledge_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_knowledgeDetailsFragment_to_editKnowledgeFragment"
            app:destination="@id/editKnowledgeFragment" />
    </fragment>

    <fragment
        android:id="@+id/editKnowledgeFragment"
        android:name="ir.rahavardit.ariel.ui.editknowledge.EditKnowledgeFragment"
        android:label="@string/edit"
        tools:layout="@layout/fragment_edit_knowledge">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="knowledge"
            app:argType="ir.rahavardit.ariel.data.model.Knowledge" />
        <action
            android:id="@+id/action_editKnowledgeFragment_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_knowledge"
        android:name="ir.rahavardit.ariel.ui.newknowledge.NewKnowledgeFragment"
        android:label="@string/new_knowledge"
        tools:layout="@layout/fragment_new_knowledge">
        <action
            android:id="@+id/action_nav_new_knowledge_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_faqs"
        android:name="ir.rahavardit.ariel.ui.faqs.FAQsFragment"
        android:label="@string/menu_faqs"
        tools:layout="@layout/fragment_faqs">
        <action
            android:id="@+id/action_nav_faqs_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment" />
        <action
            android:id="@+id/action_nav_faqs_to_nav_new_faq"
            app:destination="@id/nav_new_faq" />
    </fragment>

    <fragment
        android:id="@+id/faqDetailsFragment"
        android:name="ir.rahavardit.ariel.ui.faqdetails.FAQDetailsFragment"
        android:label="@string/faq_details"
        tools:layout="@layout/fragment_faq_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_faqDetailsFragment_to_editFAQFragment"
            app:destination="@id/editFAQFragment" />
    </fragment>

    <fragment
        android:id="@+id/editFAQFragment"
        android:name="ir.rahavardit.ariel.ui.editfaq.EditFAQFragment"
        android:label="@string/edit"
        tools:layout="@layout/fragment_edit_faq">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="faq"
            app:argType="ir.rahavardit.ariel.data.model.FAQ" />
        <action
            android:id="@+id/action_editFAQFragment_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_faq"
        android:name="ir.rahavardit.ariel.ui.newfaq.NewFAQFragment"
        android:label="@string/new_faq"
        tools:layout="@layout/fragment_new_faq">
        <action
            android:id="@+id/action_nav_new_faq_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>



    <fragment
        android:id="@+id/nav_users"
        android:name="ir.rahavardit.ariel.ui.users.UsersFragment"
        android:label="@string/menu_users"
        tools:layout="@layout/fragment_users" />

    <fragment
        android:id="@+id/nav_new_transaction"
        android:name="ir.rahavardit.ariel.ui.newtransaction.NewTransactionFragment"
        android:label="@string/new_transaction"
        tools:layout="@layout/fragment_new_transaction" />

    <fragment
        android:id="@+id/editTransactionFragment"
        android:name="ir.rahavardit.ariel.ui.edittransaction.EditTransactionFragment"
        android:label="@string/edit_transaction"
        tools:layout="@layout/fragment_edit_transaction">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="incomeObject"
            app:argType="ir.rahavardit.ariel.data.model.IncomeObject"
            app:nullable="true" />
        <argument
            android:name="expenditureObject"
            app:argType="ir.rahavardit.ariel.data.model.ExpenditureObject"
            app:nullable="true" />
        <action
            android:id="@+id/action_editTransactionFragment_to_nav_home"
            app:destination="@id/nav_home"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/searchResultsFragment"
        android:name="ir.rahavardit.ariel.ui.search.SearchResultsFragment"
        android:label="@string/search_results"
        tools:layout="@layout/fragment_search_results">
        <argument
            android:name="query"
            app:argType="string" />
        <action
            android:id="@+id/action_searchResultsFragment_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_searchResultsFragment_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment" />
        <action
            android:id="@+id/action_searchResultsFragment_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment" />
    </fragment>
</navigation>
