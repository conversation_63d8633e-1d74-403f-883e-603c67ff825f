package ir.rahavardit.ariel.ui.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.FAQ
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.data.model.Ticket
import ir.rahavardit.ariel.databinding.FragmentSearchResultsBinding
import ir.rahavardit.ariel.ui.faqs.FAQAdapter
import ir.rahavardit.ariel.ui.knowledges.KnowledgeAdapter
import ir.rahavardit.ariel.ui.tickets.TicketAdapter
import ir.rahavardit.ariel.data.SessionManager

/**
 * Fragment for displaying search results.
 */
class SearchResultsFragment : Fragment() {

    private var _binding: FragmentSearchResultsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: SearchResultsViewModel
    private lateinit var sessionManager: SessionManager

    private lateinit var ticketAdapter: TicketAdapter
    private lateinit var knowledgeAdapter: KnowledgeAdapter
    private lateinit var faqAdapter: FAQAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSearchResultsBinding.inflate(inflater, container, false)
        viewModel = ViewModelProvider(this)[SearchResultsViewModel::class.java]
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupRecyclerViews()
        observeViewModel()

        // Get the search query from arguments
        arguments?.getString("query")?.let { query ->
            performSearch(query)
        }
    }

    /**
     * Sets up the RecyclerViews for displaying search results.
     */
    private fun setupRecyclerViews() {
        // Setup Tickets RecyclerView
        ticketAdapter = TicketAdapter { ticket ->
            navigateToTicketDetails(ticket)
        }
        binding.recyclerTickets.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = ticketAdapter
            isNestedScrollingEnabled = false
        }



        // Setup Knowledges RecyclerView
        knowledgeAdapter = KnowledgeAdapter { knowledge ->
            navigateToKnowledgeDetails(knowledge)
        }
        binding.recyclerKnowledges.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = knowledgeAdapter
            isNestedScrollingEnabled = false
        }

        // Setup FAQs RecyclerView
        faqAdapter = FAQAdapter { faq ->
            navigateToFAQDetails(faq)
        }
        binding.recyclerFaqs.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = faqAdapter
            isNestedScrollingEnabled = false
        }
    }

    /**
     * Observes changes in the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.searchQuery.observe(viewLifecycleOwner) { query ->
            binding.tvSearchQuery.text = getString(R.string.search_query, query)
        }

        viewModel.tickets.observe(viewLifecycleOwner) { tickets ->
            ticketAdapter.submitList(tickets)
            binding.tvNoTickets.visibility = if (tickets.isEmpty()) View.VISIBLE else View.GONE
            binding.recyclerTickets.visibility = if (tickets.isEmpty()) View.GONE else View.VISIBLE
        }



        viewModel.knowledges.observe(viewLifecycleOwner) { knowledges ->
            knowledgeAdapter.submitList(knowledges)
            binding.tvNoKnowledges.visibility = if (knowledges.isEmpty()) View.VISIBLE else View.GONE
            binding.recyclerKnowledges.visibility = if (knowledges.isEmpty()) View.GONE else View.VISIBLE
        }

        viewModel.faqs.observe(viewLifecycleOwner) { faqs ->
            faqAdapter.submitList(faqs)
            binding.tvNoFaqs.visibility = if (faqs.isEmpty()) View.VISIBLE else View.GONE
            binding.recyclerFaqs.visibility = if (faqs.isEmpty()) View.GONE else View.VISIBLE
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Performs a search with the given query.
     *
     * @param query The search query string.
     */
    private fun performSearch(query: String) {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.search(token, query)
        } else {
            binding.tvError.text = getString(R.string.authentication_token_not_found)
            binding.tvError.visibility = View.VISIBLE
        }
    }

    /**
     * Navigates to the ticket details screen.
     *
     * @param ticket The ticket to view details for.
     */
    private fun navigateToTicketDetails(ticket: Ticket) {
        val bundle = Bundle().apply {
            putString("shortUuid", ticket.shortUuid)
        }
        findNavController().navigate(R.id.action_searchResultsFragment_to_ticketDetailsFragment, bundle)
    }



    /**
     * Navigates to the knowledge details screen.
     *
     * @param knowledge The knowledge item to view details for.
     */
    private fun navigateToKnowledgeDetails(knowledge: Knowledge) {
        val bundle = Bundle().apply {
            putString("shortUuid", knowledge.shortUuid)
        }
        findNavController().navigate(R.id.action_searchResultsFragment_to_knowledgeDetailsFragment, bundle)
    }

    /**
     * Navigates to the FAQ details screen.
     *
     * @param faq The FAQ item to view details for.
     */
    private fun navigateToFAQDetails(faq: FAQ) {
        val bundle = Bundle().apply {
            putString("shortUuid", faq.shortUuid)
        }
        findNavController().navigate(R.id.action_searchResultsFragment_to_faqDetailsFragment, bundle)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
