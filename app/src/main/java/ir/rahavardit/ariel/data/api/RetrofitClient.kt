package ir.rahavardit.ariel.data.api

import ir.rahavardit.ariel.BuildConfig

import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Singleton class for creating and providing Retrofit client instances.
 */
object RetrofitClient {

    // Default base URL for the API - can be overridden
    private var baseUrl = BuildConfig.BASE_URL

    /**
     * Sets the base URL for API requests.
     *
     * @param url The base URL to use for API requests.
     */
    fun setBaseUrl(url: String) {
        baseUrl = url
    }

    /**
     * Creates and returns an instance of the API service interface.
     *
     * @return An implementation of the ApiService interface.
     */
    fun getApiService(): ApiService {
        return getRetrofit().create(ApiService::class.java)
    }

    /**
     * Creates and configures a Retrofit instance.
     *
     * @return A configured Retrofit instance.
     */
    private fun getRetrofit(): Retrofit {
        // Create a custom Gson instance
        val gson = GsonBuilder()
            .setLenient() // Allow malformed JSON parsing
            .create()

        return Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(getOkHttpClient())
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }

    /**
     * Creates and configures an OkHttpClient with logging and timeouts.
     *
     * @return A configured OkHttpClient instance.
     */
    private fun getOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }

        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }
}
